const ccxt = require('ccxt');
const { GenerativeModel } = require('@google/generative-ai');
const config = require('./config.json');
const db = require('./db');

const exchange = new ccxt.binance(); // Free public API

async function fetchCandles(symbol, timeframe, limit = 100) {
  const candles = await exchange.fetchOHLCV(symbol, timeframe, undefined, limit);
  candles.forEach(c => db.saveCandle({
    symbol, timeframe, timestamp: c[0], open: c[1], high: c[2], low: c[3], close: c[4], volume: c[5]
  }));
  return candles;
}

async function getLivePrice(symbol) {
  const ticker = await exchange.fetchTicker(symbol);
  return ticker.last;
}

const gemini = new GenerativeModel('gemini-1.5-flash', { apiKey: config.geminiApiKey });

async function generateExplanation(signal) {
  const prompt = `Explain this trading signal in simple terms: Symbol ${signal.symbol}, Direction ${signal.direction}, Score ${signal.confluence_score}. Components: ${JSON.stringify(signal.components)}.`;
  const response = await gemini.generateContent(prompt);
  return response.text;
}

function addDisclaimer(msg) {
  return `${msg}\n\n${config.disclaimer}`;
}

module.exports = { fetchCandles, getLivePrice, generateExplanation, addDisclaimer };