const tf = require('@tensorflow/tfjs-node');

// Simple model: Train on dummy data (expand with real)
let model;

async function loadOrTrainModel() {
  model = tf.sequential();
  model.add(tf.layers.dense({ units: 10, activation: 'relu', inputShape: [4] })); // 4 components
  model.add(tf.layers.dense({ units: 1, activation: 'sigmoid' }));
  model.compile({ optimizer: 'adam', loss: 'binary_crossentropy' });

  // Dummy training data: features [structure, ob, fvg, liquidity], label: good signal (1/0)
  const xs = tf.tensor2d([[1,1,1,1], [0,0,0,0], [1,0,1,0]]); // Expand
  const ys = tf.tensor2d([[1], [0], [1]]);
  await model.fit(xs, ys, { epochs: 10 });
}

async function predict(components) {
  if (!model) await loadOrTrainModel();
  const input = tf.tensor2d([[components.structure, components.ob, components.fvg, components.liquidity]]);
  const pred = model.predict(input);
  return (await pred.data())[0];
}

module.exports = { predict };