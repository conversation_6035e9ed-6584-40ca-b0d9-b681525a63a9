const mysql = require('mysql2/promise');
const config = require('./config.json');

const pool = mysql.createPool(config.db);

async function query(sql, params) {
  const [rows] = await pool.execute(sql, params);
  return rows;
}

// Example functions
async function getUser(telegramId) {
  return query('SELECT * FROM users WHERE telegram_id = ?', [telegramId]);
}

async function insertUser(user) {
  await query('INSERT INTO users (telegram_id, full_name, trader_type, region, activated) VALUES (?, ?, ?, ?, ?)', 
    [user.telegram_id, user.full_name, user.trader_type, user.region, true]);
}

async function saveCandle(candle) {
  await query('INSERT INTO candles (symbol, timeframe, timestamp, open, high, low, close, volume) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [candle.symbol, candle.timeframe, candle.timestamp, candle.open, candle.high, candle.low, candle.close, candle.volume]);
}

// Add more for signals, trades, codes, etc. (similar pattern)

module.exports = { query, getUser, insertUser, saveCandle /* add others */ };