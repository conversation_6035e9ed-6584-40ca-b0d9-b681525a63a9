const { Telegraf } = require('telegraf');
const config = require('./config.json');
const db = require('./db');
const { backtest } = require('./tradingLogic');

const bot = new Telegraf(config.adminBotToken);

// Assume admin check: Hardcode your Telegram ID for security
const adminId = 123456789; // Your ID

bot.use((ctx, next) => {
  if (ctx.from.id !== adminId) return ctx.reply('Access denied.');
  next();
});

bot.command('users', async (ctx) => {
  const users = await db.query('SELECT * FROM users');
  ctx.reply(`Users: ${JSON.stringify(users, null, 2)}`);
});

bot.command('generate_code', async (ctx) => {
  const code = Math.random().toString(36).substring(7);
  const expiry = new Date(Date.now() + 86400000); // 1 day
  await db.query('INSERT INTO activation_codes (code, expiry, uses_left) VALUES (?, ?, 1)', [code, expiry]);
  ctx.reply(`New code: ${code}`);
});

bot.command('broadcast', async (ctx) => {
  const msg = ctx.message.text.split(' ').slice(1).join(' ');
  const users = await db.query('SELECT telegram_id FROM users');
  for (let user of users) {
    bot.telegram.sendMessage(user.telegram_id, msg);
  }
  ctx.reply('Broadcast sent.');
});

bot.command('stats', async (ctx) => {
  const winrate = await db.query('SELECT AVG(winrate) as avg FROM backtest_results');
  ctx.reply(`Avg Winrate: ${winrate[0].avg}`);
});

// Add /user <id>, /signals, deactivate, etc.

module.exports = bot;