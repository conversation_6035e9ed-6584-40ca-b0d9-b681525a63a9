const { Telegraf } = require('telegraf');
const config = require('./config.json');
const db = require('./db');
const { computeConfluence, backtest } = require('./tradingLogic');
const { getLive<PERSON>rice, addDisclaimer, fetchCandles } = require('./utils');

const bot = new Telegraf(config.userBotToken);

bot.start(async (ctx) => {
  const userId = ctx.from.id;
  const user = await db.getUser(userId);
  if (!user.length) {
    // Activation code prompt
    ctx.reply('Enter activation code:');
    bot.on('text', async (ctx) => {
      const code = ctx.message.text;
      const codeRow = await db.query('SELECT * FROM activation_codes WHERE code = ? AND uses_left > 0 AND expiry > NOW()', [code]);
      if (codeRow.length) {
        // Onboarding
        ctx.reply('Full name:');
        // Handle sequential inputs (use scenes or callbacks for production)
        // For simplicity: Assume next messages are name, then buttons for type/region
        await db.insertUser({ telegram_id: userId, full_name: 'Sample', trader_type: 'real', region: 'Asia' });
        await db.query('UPDATE activation_codes SET uses_left = uses_left - 1 WHERE code = ?', [code]);
        if (trader_type === 'binary') await db.query('UPDATE users SET binary_opt_in = TRUE WHERE id = ?', [userId]); // Opt-in
        ctx.reply('Activated!');
      } else {
        ctx.reply('Invalid code.');
      }
    });
  } else {
    ctx.reply('Welcome back!');
  }
});

bot.command('price', async (ctx) => {
  const symbol = ctx.message.text.split(' ')[1];
  const price = await getLivePrice(symbol);
  ctx.reply(`${symbol}: ${price}`);
});

bot.command('trade', async (ctx) => {
  const userId = ctx.from.id;
  const user = await db.getUser(userId)[0];
  if (!user.activated) return ctx.reply('Activate first.');

  // Check risk: max trades, cooldown
  const recentTrades = await db.query('SELECT * FROM user_trades WHERE user_id = ? AND resolved_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)', [user.id]);
  if (recentTrades.length >= config.risk.maxTradesPerUser) return ctx.reply('Max trades reached.');

  const candles = await fetchCandles('BTC/USDT', '1h'); // Default
  const signal = await computeConfluence(candles, 'BTC/USDT', '1h');
  if (signal) {
    const msg = `Signal: ${signal.direction} at ${signal.entry}. SL: ${signal.sl}, TP: ${signal.tp}. Score: ${signal.confluence_score}.\n${signal.explanation}`;
    ctx.reply(addDisclaimer(msg));
    await db.query('INSERT INTO user_trades (user_id, signal_id) VALUES (?, ?)', [user.id, signal.id]);
  } else {
    ctx.reply('No signal now.');
  }
});

// Add /profile, /settings, /menu similarly (query DB, update with inputs)

// Outcome resolver (run every 5min)
setInterval(async () => {
  const pending = await db.query('SELECT * FROM user_trades WHERE outcome = "pending"');
  for (let trade of pending) {
    const signal = await db.query('SELECT * FROM signals WHERE id = ?', [trade.signal_id])[0];
    const latestCandle = await fetchCandles(signal.symbol, signal.timeframe, 1)[0];
    // Check SL/TP hit
    const outcome = /* logic: if high >= tp 'win', low <= sl 'loss' */ 'win';
    await db.query('UPDATE user_trades SET outcome = ?, resolved_at = NOW() WHERE id = ?', [outcome, trade.id]);
  }
}, 300000);

module.exports = bot;