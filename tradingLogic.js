const tf = require('@tensorflow/tfjs-node');
const config = require('./config.json');
const { fetchCandles } = require('./utils');
const db = require('./db');

// ICT/SMC Functions
function detectPivot(candles, index) { // 2-bar pivot for BOS/CHOCH
  if (index < 2 || index >= candles.length - 1) return false;
  const high = candles[index][2];
  return high > candles[index-1][2] && high > candles[index+1][2]; // High pivot
  // Add low pivot similarly
}

function findOrderBlock(candles, index) { // Last opposite before move
  // Simplified: Find last bearish candle before bullish move
  for (let i = index; i > 0; i--) {
    if (candles[i][4] < candles[i][1]) return { level: candles[i][3] }; // Bearish close < open
  }
  return null;
}

function detectFVG(candles, index) { // 3-candle gap
  if (index < 2) return false;
  const gap = candles[index-2][2] - candles[index][3]; // High of 2 ago - low of current
  return gap > 0; // Positive gap
}

function detectLiquiditySweep(candles, index) { // Wick clears high/low then reverses
  const wick = candles[index][2] - candles[index][4]; // Upper wick
  return wick > (candles[index][4] - candles[index][3]) * 2 && candles[index+1][4] < candles[index][4]; // Large wick + reverse
}

async function computeConfluence(candles, symbol, timeframe) {
  const components = {
    structure: detectPivot(candles, candles.length - 1) ? 1 : 0,
    ob: findOrderBlock(candles, candles.length - 1) ? 1 : 0,
    fvg: detectFVG(candles, candles.length - 1) ? 1 : 0,
    liquidity: detectLiquiditySweep(candles, candles.length - 1) ? 1 : 0
  };

  // ML Scoring (use TF.js model)
  const mlScore = await mlModel.predict(components); // Returns 0-1

  const score = (mlScore * config.confluenceWeights.ml) +
    (components.structure * config.confluenceWeights.structure) +
    (components.ob * config.confluenceWeights.ob) +
    (components.fvg * config.confluenceWeights.fvg) +
    (components.liquidity * config.confluenceWeights.liquidity);

  if (score < config.confluenceThreshold) return null;

  const direction = candles[candles.length - 1][4] > candles[candles.length - 2][4] ? 'buy' : 'sell';
  const entry = candles[candles.length - 1][4];
  const sl = direction === 'buy' ? entry * 0.98 : entry * 1.02; // 2% SL
  const tp = direction === 'buy' ? entry + (entry - sl) * config.risk.slTpRatio : entry - (sl - entry) * config.risk.slTpRatio;

  const signal = { symbol, timeframe, timestamp: Date.now(), direction, confluence_score: score, components, entry, sl, tp, version: '1.0' };
  signal.explanation = await utils.generateExplanation(signal);

  await db.query('INSERT INTO signals SET ?', [signal]);
  return signal;
}

// Backtesting (walk-forward)
async function backtest(symbol, timeframe) {
  const historical = await fetchCandles(symbol, timeframe, 500); // Last 500 candles
  let wins = 0, losses = 0, equity = 10000;
  for (let i = 100; i < historical.length; i++) { // Skip first for lookback
    const subCandles = historical.slice(0, i);
    const signal = await computeConfluence(subCandles, symbol, timeframe);
    if (signal) {
      // Simulate: Check if TP or SL hit in next candles
      const next = historical[i];
      const hitTp = signal.direction === 'buy' ? next[2] >= signal.tp : next[3] <= signal.tp;
      const hitSl = signal.direction === 'buy' ? next[3] <= signal.sl : next[2] >= signal.sl;
      if (hitTp) wins++;
      else if (hitSl) losses++;
    }
  }
  const winrate = (wins / (wins + losses)) * 100 || 0;
  // Add profit_factor, etc. similarly
  await db.query('INSERT INTO backtest_results (symbol, timeframe, winrate, tested_at) VALUES (?, ?, ?, NOW())', [symbol, timeframe, winrate]);
  return { winrate };
}

module.exports = { computeConfluence, backtest };